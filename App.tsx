import React, { useState, useEffect } from 'react';
import { AnalysisResult, AppState, PreliminarySummary } from './types';
import { getPreliminarySummary, analyzeChart } from './services/geminiService';
import UploadStep from './components/UploadStep';
import ConfirmStep from './components/ConfirmStep';
import LoadingSpinner from './components/LoadingSpinner';
import AnalysisResultComponent from './components/AnalysisResult';
import ErrorDisplay from './components/ErrorDisplay';
import { Icon } from './components/Icon';


const App: React.FC = () => {
  const [appState, setAppState] = useState<AppState>('UPLOAD');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [preliminarySummary, setPreliminarySummary] = useState<PreliminarySummary | null>(null);
  const [userNotes, setUserNotes] = useState<string>('');
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileUpload = (file: File) => {
    setImageFile(file);
    setImageUrl(URL.createObjectURL(file));
    setAppState('PRE_ANALYZING');
  };

  const handleAnalyzeWithNotes = (notes: string) => {
    setUserNotes(notes);
    setAppState('ANALYZING');
  };
  
  const handleConfirmSummary = () => {
    setUserNotes(''); // Pastikan tidak ada catatan lama
    setAppState('ANALYZING');
  };

  const resetState = () => {
    if(imageUrl) URL.revokeObjectURL(imageUrl);
    setImageFile(null);
    setImageUrl(null);
    setPreliminarySummary(null);
    setUserNotes('');
    setAnalysisResult(null);
    setError(null);
    setAppState('UPLOAD');
  }

  useEffect(() => {
    if (appState === 'PRE_ANALYZING' && imageFile) {
      const fetchSummary = async () => {
        setError(null);
        try {
          const summaryObject = await getPreliminarySummary(imageFile);
          setPreliminarySummary(summaryObject);
          setAppState('CONFIRM_SUMMARY');
        } catch (err) {
          console.error(err);
          setError(err instanceof Error ? err.message : 'Gagal membuat ringkasan awal.');
          setAppState('ERROR');
        }
      };
      fetchSummary();
    }
  }, [appState, imageFile]);

  useEffect(() => {
    if (appState === 'ANALYZING' && imageFile && preliminarySummary) {
        const doAnalysis = async () => {
             setError(null);
             try {
                const result = await analyzeChart(imageFile, preliminarySummary, userNotes || undefined);
                setAnalysisResult(result);
                setAppState('RESULT');
            } catch (err) {
                console.error(err);
                setError(err instanceof Error ? err.message : 'Terjadi kesalahan yang tidak diketahui selama analisis.');
                setAppState('ERROR');
            }
        };
        doAnalysis();
    }
  }, [appState, imageFile, userNotes, preliminarySummary]);

  const renderContent = () => {
    switch (appState) {
      case 'UPLOAD':
        return <UploadStep onFileUpload={handleFileUpload} />;
      case 'PRE_ANALYZING':
        return <LoadingSpinner text="Membuat kesimpulan awal..." />;
      case 'CONFIRM_SUMMARY':
        return <ConfirmStep 
                  imageUrl={imageUrl!} 
                  summary={preliminarySummary!.summary}
                  currentPrice={preliminarySummary!.currentPrice}
                  onConfirm={handleConfirmSummary}
                  onAnalyzeWithNotes={handleAnalyzeWithNotes}
                  onReject={resetState} 
               />;
      case 'ANALYZING':
        return <LoadingSpinner text="Menganalisis grafik dengan AI... ini mungkin perlu beberapa saat." />;
      case 'RESULT':
        return <AnalysisResultComponent result={analysisResult!} onReset={resetState} />;
      case 'ERROR':
        return <ErrorDisplay message={error!} onReset={resetState} />;
      default:
        return <UploadStep onFileUpload={handleFileUpload} />;
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 text-slate-100 flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8 font-sans">
      <div className="w-full max-w-4xl mx-auto">
        <header className="text-center mb-8">
            <div className="flex items-center justify-center gap-4">
                <Icon name="brainCircuit" className="h-10 w-10 text-cyan-400" />
                <h1 className="text-3xl sm:text-4xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-blue-500">
                    Penganalisis Grafik Pasar AI
                </h1>
            </div>
          <p className="mt-3 text-lg text-slate-400 max-w-2xl mx-auto">
            Unggah grafik pasar untuk mendapatkan analisis teknis dan sinyal perdagangan bertenaga AI.
          </p>
        </header>
        <main className="bg-slate-800/50 rounded-2xl shadow-2xl shadow-slate-950/50 p-6 sm:p-8 ring-1 ring-slate-700">
            {renderContent()}
        </main>
        <footer className="text-center mt-8 text-slate-500 text-sm">
            <p>&copy; {new Date().getFullYear()} Alat Keuangan AI. Analisis hanya untuk tujuan informasi.</p>
        </footer>
      </div>
    </div>
  );
};

export default App;