import { GoogleGenAI, Type } from "@google/genai";
import { AnalysisResult, PreliminarySummary } from '../types';

if (!process.env.API_KEY) {
    throw new Error("Variabel lingkungan API_KEY tidak diatur");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

const fileToGenerativePart = async (file: File) => {
  const base64EncodedDataPromise = new Promise<string>((resolve) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      if (reader.result) {
        resolve((reader.result as string).split(',')[1]);
      } else {
        resolve("");
      }
    };
    reader.readAsDataURL(file);
  });

  return {
    inlineData: { data: await base64EncodedDataPromise, mimeType: file.type },
  };
};

const summarySchema = {
    type: Type.OBJECT,
    properties: {
        summary: {
            type: Type.STRING,
            description: "Jelaskan gambar grafik dalam satu atau dua kalimat. Identifikasi aset, kerangka waktu yang mungkin, dan tren atau pola umum yang terlihat.",
        },
        currentPrice: {
            type: Type.STRING,
            description: "Identifikasi harga pasar saat ini atau harga penutupan kandil terakhir yang terlihat pada grafik. Format sebagai string (misalnya, '$45,123.45' atau '1.0850'). Jika tidak terlihat, kembalikan 'N/A'."
        }
    },
    required: ["summary", "currentPrice"],
};

const analysisSchema = {
    type: Type.OBJECT,
    properties: {
        signal: {
            type: Type.STRING,
            description: "Sinyal perdagangan: BUY, SELL, atau NEUTRAL.",
            enum: ["BUY", "SELL", "NEUTRAL"],
        },
        confidence: {
            type: Type.INTEGER,
            description: "Skor keyakinan untuk sinyal dari 0 hingga 100.",
        },
        summary: {
            type: Type.STRING,
            description: "Satu kalimat yang merangkum dasar pemikiran untuk sinyal tersebut.",
        },
        reasoning: {
            type: Type.OBJECT,
            description: "Analisis rinci berdasarkan indikator teknis.",
            properties: {
                smartMoneyConcept: {
                    type: Type.STRING,
                    description: "Analisis Konsep Smart Money (SMC), termasuk akumulasi atau distribusi.",
                },
                supportAndResistance: {
                    type: Type.STRING,
                    description: "Analisis level support dan resistance utama.",
                },
                trendAnalysis: {
                    type: Type.STRING,
                    description: "Analisis tren pasar menggunakan Higher Highs/Higher Lows atau Lower Highs/Lower Lows.",
                },
                orderBlock: {
                    type: Type.STRING,
                    description: "Identifikasi dan analisis order block signifikan yang mengindikasikan potensi pembalikan arah.",
                },
                ema: {
                    type: Type.STRING,
                    description: "Analisis Exponential Moving Averages (EMA) jika terlihat pada grafik (misalnya, EMA 20, 50, 200).",
                },
                marketCondition: {
                    type: Type.STRING,
                    description: "Analisis kondisi pasar secara keseluruhan, seperti trending atau sideways (terbatas dalam rentang).",
                },
            },
            required: ["smartMoneyConcept", "supportAndResistance", "trendAnalysis", "orderBlock", "ema", "marketCondition"]
        },
        takeProfit: {
            type: Type.OBJECT,
            description: "Level Take Profit (TP) yang direkomendasikan.",
            properties: {
                tp1: { type: Type.STRING, description: "Level Take Profit 1, biasanya di level resistance/support terdekat berikutnya." },
                tp2: { type: Type.STRING, description: "Level Take Profit 2, target keuntungan sekunder." },
                tp3: { type: Type.STRING, description: "Level Take Profit 3, target keuntungan jangka panjang atau pada level kunci." },
            },
            required: ["tp1", "tp2", "tp3"]
        },
        stopLoss: {
            type: Type.STRING,
            description: "Level Stop Loss (SL) yang direkomendasikan untuk mengelola risiko. Untuk sinyal BELI, tempatkan sedikit di bawah support kunci. Untuk sinyal JUAL, tempatkan sedikit di atas resistance kunci."
        }
    },
    required: ["signal", "confidence", "summary", "reasoning", "takeProfit", "stopLoss"],
};

export const getPreliminarySummary = async (imageFile: File): Promise<PreliminarySummary> => {
    const imagePart = await fileToGenerativePart(imageFile);
    const prompt = `Anda adalah seorang analis grafik pasar yang ahli. Lihatlah gambar grafik yang disediakan. 
1. Jelaskan secara singkat dalam satu atau dua kalimat apa yang Anda lihat. Identifikasi kemungkinan aset (misalnya, BTC/USD), kerangka waktu yang mungkin (misalnya, grafik 4 jam), dan tren atau pola umum saat ini.
2. Identifikasi harga pasar saat ini (harga penutupan kandil terakhir) dari grafik.
Respons Anda HARUS berupa objek JSON yang valid yang berisi kunci "summary" dan "currentPrice".`;

    const response = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: { parts: [{ text: prompt }, imagePart] },
        config: {
            responseMimeType: "application/json",
            responseSchema: summarySchema,
            temperature: 0.1
        }
    });

    try {
        const parsedJson = JSON.parse(response.text.trim());
        return parsedJson as PreliminarySummary;
    } catch (e) {
        console.error("Gagal mem-parsing ringkasan JSON:", response.text);
        throw new Error("AI mengembalikan format ringkasan yang tidak valid.");
    }
};

export const analyzeChart = async (imageFile: File, preliminarySummary: PreliminarySummary, userNotes?: string): Promise<AnalysisResult> => {
    const imagePart = await fileToGenerativePart(imageFile);

    let prompt = `Anda adalah seorang analis pasar keuangan ahli yang berspesialisasi dalam analisis teknis dari gambar grafik. Analisis gambar grafik pasar yang disediakan.
    
Konteks awal yang diidentifikasi dari gambar adalah:
- Ringkasan: "${preliminarySummary.summary}"
- Harga Saat Ini yang Terlihat: "${preliminarySummary.currentPrice}"

Berdasarkan informasi visual, evaluasi grafik menggunakan indikator berikut: Smart Money Concept (SMC), Support & Resistance (S&R), struktur pasar (Higher Highs/Higher Lows untuk tren bullish, Lower Highs/Lower Lows untuk tren bearish), Order Blocks, Exponential Moving Averages (jika terlihat), dan kondisi pasar (trending atau sideways).

Berdasarkan analisis komprehensif Anda, berikan sinyal perdagangan (BUY, SELL, atau NEUTRAL). Selain itu, tentukan tiga level Take Profit (TP1, TP2, TP3) dan satu level Stop Loss (SL).

- Untuk sinyal BELI, tetapkan level TP pada level resistance berikutnya dan level SL sedikit di bawah level support kunci.
- Untuk sinyal JUAL, tetapkan level TP pada level support berikutnya dan level SL sedikit di atas level resistance kunci.
- Jika sinyal NETRAL, berikan 'N/A' untuk level TP dan SL.

Respons Anda HARUS berupa objek JSON yang valid yang secara ketat mematuhi skema yang disediakan. Jangan sertakan teks, format markdown, atau penjelasan apa pun di luar struktur JSON.

Berikan dasar pemikiran yang terperinci untuk setiap indikator dalam objek 'reasoning'. Jadilah teliti dan profesional dalam penjelasan Anda.`;
    
    if (userNotes) {
        prompt += `\n\nPENTING: Pengguna telah memberikan koreksi atau konteks berikut: "${userNotes}". Anda HARUS mempertimbangkan catatan ini untuk menyempurnakan analisis Anda. Jika catatan pengguna bertentangan dengan interpretasi awal Anda, prioritaskan masukan pengguna.`;
    }


    const response = await ai.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: { parts: [{ text: prompt }, imagePart] },
        config: {
            responseMimeType: "application/json",
            responseSchema: analysisSchema,
            temperature: 0.2
        }
    });

    const jsonText = response.text.trim();
    try {
        const parsedJson = JSON.parse(jsonText);
        return parsedJson as AnalysisResult;
    } catch (e) {
        console.error("Gagal mem-parsing respons JSON:", jsonText);
        throw new Error("AI mengembalikan format respons yang tidak valid. Silakan coba lagi.");
    }
};