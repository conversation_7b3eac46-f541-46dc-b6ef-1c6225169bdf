
import React from 'react';
import { Icon } from './Icon';

interface LoadingSpinnerProps {
  text?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ text }) => {
  return (
    <div className="flex flex-col items-center justify-center py-20 text-center">
      <Icon name="brainCircuit" className="h-16 w-16 text-cyan-400 animate-pulse" />
      <p className="mt-4 text-lg font-semibold text-slate-300 animate-pulse">
        {text || 'Memuat...'}
      </p>
    </div>
  );
};

export default LoadingSpinner;