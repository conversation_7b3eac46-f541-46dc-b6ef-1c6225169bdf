import React, { useState } from 'react';
import { Icon } from './Icon';

interface ConfirmStepProps {
  imageUrl: string;
  summary: string;
  currentPrice: string;
  onConfirm: () => void;
  onAnalyzeWithNotes: (notes: string) => void;
  onReject: () => void;
}

const ConfirmStep: React.FC<ConfirmStepProps> = ({ imageUrl, summary, currentPrice, onConfirm, onAnalyzeWithNotes, onReject }) => {
  const [showNotes, setShowNotes] = useState(false);
  const [notes, setNotes] = useState('');

  const handleCorrectionClick = () => {
    setShowNotes(true);
  };

  const handleSubmitWithNotes = () => {
    if (notes.trim()) {
      onAnalyzeWithNotes(notes);
    }
  };

  return (
    <div className="flex flex-col lg:flex-row gap-8 items-start">
        <div className="lg:w-1/2 w-full">
            <h2 className="text-2xl font-bold text-slate-100 mb-2 text-center lg:text-left">Konfirmasi Kesimpulan Awal</h2>
            <p className="text-slate-400 mb-6 text-center lg:text-left">AI telah meringkas grafik Anda. Harap konfirmasi apakah ini benar, atau berikan catatan untuk analisis yang lebih akurat.</p>
            
            <div className="bg-slate-900/70 p-5 rounded-lg ring-1 ring-slate-700 mb-6">
                <div className="space-y-4">
                    <div>
                        <h3 className="font-semibold text-slate-300 mb-1">Kesimpulan AI:</h3>
                        <p className="text-slate-400 italic">"{summary}"</p>
                    </div>
                    <div className="border-t border-slate-700"></div>
                    <div>
                        <h3 className="font-semibold text-slate-300 mb-1">Harga Terdeteksi:</h3>
                        <div className="flex items-center gap-2">
                           <Icon name="dollarSign" className="h-6 w-6 text-cyan-400"/>
                           <p className="text-cyan-400 text-2xl font-mono font-bold">{currentPrice}</p>
                        </div>
                    </div>
                </div>
            </div>

            {!showNotes ? (
                 <div className="space-y-4">
                    <p className="font-semibold text-center text-slate-200">Apakah kesimpulan ini benar?</p>
                    <div className="flex flex-col sm:flex-row items-center gap-4">
                        <button
                            onClick={onConfirm}
                            className="flex items-center justify-center gap-2 w-full px-8 py-3 font-semibold text-white bg-cyan-600 hover:bg-cyan-500 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900 focus:ring-cyan-500"
                            >
                            <Icon name="checkCircle" className="h-5 w-5" />
                            <span>Ya, Benar & Lanjutkan</span>
                        </button>
                        <button
                            onClick={handleCorrectionClick}
                            className="flex items-center justify-center gap-2 w-full px-6 py-3 font-semibold text-slate-100 bg-slate-600 hover:bg-slate-500 rounded-md transition-all duration-200"
                        >
                            <Icon name="xCircle" className="h-5 w-5" />
                            <span>Tidak, Perlu Koreksi</span>
                        </button>
                    </div>
                </div>
            ) : (
                <div className="space-y-4">
                    <label htmlFor="notes" className="font-semibold text-slate-200 block">Berikan catatan untuk membantu AI:</label>
                    <textarea 
                        id="notes"
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        placeholder="Contoh: 'Ini adalah grafik 1 jam, bukan 4 jam.' atau 'Fokus pada pola head and shoulders.'"
                        className="w-full h-24 p-3 bg-slate-900 border border-slate-600 rounded-md text-slate-300 placeholder-slate-500 focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition"
                    />
                    <button
                        onClick={handleSubmitWithNotes}
                        disabled={!notes.trim()}
                        className="flex items-center justify-center gap-2 w-full px-8 py-3 font-semibold text-white bg-cyan-600 hover:bg-cyan-500 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900 focus:ring-cyan-500 disabled:bg-slate-700 disabled:text-slate-500 disabled:cursor-not-allowed"
                    >
                         <Icon name="arrowRight" className="h-5 w-5" />
                        <span>Kirim Koreksi & Analisis Ulang</span>
                    </button>
                </div>
            )}
            <div className="text-center mt-6">
                 <button onClick={onReject} className="text-slate-500 hover:text-slate-400 text-sm transition-colors">
                    Kembali & Unggah Ulang
                </button>
            </div>
        </div>
        <div className="lg:w-1/2 w-full mt-8 lg:mt-0">
            <div className="rounded-lg overflow-hidden shadow-lg ring-1 ring-slate-700">
                <img src={imageUrl} alt="Pratinjau grafik pasar" className="w-full h-auto object-contain" />
            </div>
        </div>
    </div>
  );
};

export default ConfirmStep;