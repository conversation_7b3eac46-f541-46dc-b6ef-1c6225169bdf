import React from 'react';
import { AnalysisResult, Reasoning, Signal, TakeProfit } from '../types';
import { Icon } from './Icon';

interface AnalysisResultProps {
  result: AnalysisResult;
  onReset: () => void;
}

const SignalBadge: React.FC<{ signal: Signal }> = ({ signal }) => {
  const signalStyles = {
    BUY: 'bg-green-500/10 text-green-400 ring-green-500/30',
    SELL: 'bg-red-500/10 text-red-400 ring-red-500/30',
    NEUTRAL: 'bg-yellow-500/10 text-yellow-400 ring-yellow-500/30',
  };
  const signalIcons = {
    BUY: 'trendingUp',
    SELL: 'trendingDown',
    NEUTRAL: 'moveHorizontal',
  }
  const signalLabels = {
      BUY: 'BELI',
      SELL: 'JUAL',
      NEUTRAL: 'NETRAL'
  }
  return (
    <div className={`inline-flex items-center gap-3 px-6 py-3 rounded-full font-bold text-2xl ring-1 ${signalStyles[signal]}`}>
        <Icon name={signalIcons[signal]} className="h-7 w-7" />
        <span>SINYAL {signalLabels[signal]}</span>
    </div>
  );
};

const ConfidenceCircle: React.FC<{ confidence: number }> = ({ confidence }) => {
    const radius = 50;
    const circumference = 2 * Math.PI * radius;
    const offset = circumference - (confidence / 100) * circumference;

    return (
        <div className="relative flex items-center justify-center">
            <svg className="transform -rotate-90" width="120" height="120">
                <circle className="text-slate-700" strokeWidth="8" stroke="currentColor" fill="transparent" r={radius} cx="60" cy="60" />
                <circle className="text-cyan-400" strokeWidth="8" strokeDasharray={circumference} strokeDashoffset={offset} strokeLinecap="round" stroke="currentColor" fill="transparent" r={radius} cx="60" cy="60" />
            </svg>
            <span className="absolute text-3xl font-bold text-slate-100">{confidence}%</span>
        </div>
    );
};

const ReasoningCard: React.FC<{ icon: string; title: string; text: string }> = ({ icon, title, text }) => (
    <div className="bg-slate-900/70 p-5 rounded-lg ring-1 ring-slate-700 h-full">
        <div className="flex items-center gap-3 mb-3">
            <Icon name={icon} className="h-6 w-6 text-cyan-400" />
            <h4 className="font-semibold text-lg text-slate-200">{title}</h4>
        </div>
        <p className="text-slate-400 text-sm leading-relaxed">{text}</p>
    </div>
);

const reasoningMap: { [K in keyof Reasoning]: { icon: string; title: string } } = {
    smartMoneyConcept: { icon: 'dollarSign', title: 'Konsep Smart Money' },
    supportAndResistance: { icon: 'barChart', title: 'Support & Resistance' },
    trendAnalysis: { icon: 'trendingUp', title: 'Analisis Tren' },
    orderBlock: { icon: 'box', title: 'Order Block' },
    ema: { icon: 'trendingUp', title: 'Analisis EMA' },
    marketCondition: { icon: 'moveHorizontal', title: 'Kondisi Pasar' },
};

const TPSLDisplay: React.FC<{ takeProfit: TakeProfit, stopLoss: string, signal: Signal }> = ({ takeProfit, stopLoss, signal }) => {
    const isNeutral = signal === 'NEUTRAL';
    const tpColor = "text-green-400";
    const slColor = "text-red-400";
    
    if (isNeutral && stopLoss === 'N/A' && takeProfit.tp1 === 'N/A') {
        return null;
    }

    return (
        <div className="bg-slate-800 p-6 rounded-xl ring-1 ring-slate-700">
            <h3 className="text-2xl font-bold text-slate-100 mb-5 text-center">Level Perdagangan yang Direkomendasikan</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Take Profit Section */}
                <div className="flex flex-col gap-3">
                    <div className="flex items-center gap-3 text-lg font-semibold text-slate-300">
                        <Icon name="target" className={`h-6 w-6 ${tpColor}`} />
                        <span>Take Profit (TP)</span>
                    </div>
                    <div className="pl-9 space-y-2">
                         <p className={`flex justify-between items-center text-slate-400`}>
                            <span>TP 1:</span> <span className={`font-mono text-lg font-bold ${tpColor}`}>{takeProfit.tp1}</span>
                         </p>
                         <p className={`flex justify-between items-center text-slate-400`}>
                            <span>TP 2:</span> <span className={`font-mono text-lg font-bold ${tpColor}`}>{takeProfit.tp2}</span>
                         </p>
                         <p className={`flex justify-between items-center text-slate-400`}>
                            <span>TP 3:</span> <span className={`font-mono text-lg font-bold ${tpColor}`}>{takeProfit.tp3}</span>
                         </p>
                    </div>
                </div>
                {/* Stop Loss Section */}
                <div className="flex flex-col gap-3">
                    <div className="flex items-center gap-3 text-lg font-semibold text-slate-300">
                        <Icon name="shield" className={`h-6 w-6 ${slColor}`} />
                        <span>Stop Loss (SL)</span>
                    </div>
                    <div className="pl-9 space-y-2">
                        <p className={`flex justify-between items-center text-slate-400`}>
                             <span>SL:</span> <span className={`font-mono text-lg font-bold ${slColor}`}>{stopLoss}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};


const AnalysisResultComponent: React.FC<AnalysisResultProps> = ({ result, onReset }) => {
  return (
    <div className="flex flex-col gap-8">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center bg-slate-800 p-6 rounded-xl ring-1 ring-slate-700">
          <div className="flex flex-col items-center justify-center gap-3">
              <h3 className="text-sm font-semibold text-slate-400 uppercase tracking-wider">Sinyal</h3>
              <SignalBadge signal={result.signal} />
          </div>
          <div className="flex flex-col items-center justify-center gap-3">
              <h3 className="text-sm font-semibold text-slate-400 uppercase tracking-wider">Ringkasan</h3>
              <p className="text-slate-200 font-medium text-lg">{result.summary}</p>
          </div>
          <div className="flex flex-col items-center justify-center gap-3">
              <h3 className="text-sm font-semibold text-slate-400 uppercase tracking-wider">Keyakinan</h3>
              <ConfidenceCircle confidence={result.confidence} />
          </div>
      </div>

      <TPSLDisplay takeProfit={result.takeProfit} stopLoss={result.stopLoss} signal={result.signal} />

      <div>
        <h3 className="text-2xl font-bold text-slate-100 mb-5 text-center">Analisis Rinci</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
            {Object.entries(result.reasoning).map(([key, value]) => {
                const mapKey = key as keyof Reasoning;
                const { icon, title } = reasoningMap[mapKey] || {icon: 'brainCircuit', title: 'Analisis'};
                if (!value) return null;
                return <ReasoningCard key={key} icon={icon} title={title} text={value} />
            })}
        </div>
      </div>

      <div className="text-center mt-4">
        <button
          onClick={onReset}
          className="flex items-center justify-center gap-2 px-6 py-3 font-semibold text-white bg-cyan-600 hover:bg-cyan-500 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-cyan-500"
        >
          <Icon name="rotateCcw" className="h-5 w-5" />
          <span>Analisis Grafik Lain</span>
        </button>
      </div>
    </div>
  );
};

export default AnalysisResultComponent;