
import React, { useCallback, useState } from 'react';
import { Icon } from './Icon';

interface UploadStepProps {
  onFileUpload: (file: File) => void;
}

const UploadStep: React.FC<UploadStepProps> = ({ onFileUpload }) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      onFileUpload(e.target.files[0]);
    }
  };

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      onFileUpload(e.dataTransfer.files[0]);
    }
  }, [onFileUpload]);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  return (
    <div className="flex flex-col md:flex-row gap-8 items-center">
        <div className="flex-1 w-full">
            <div 
              className={`relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-300 ${isDragging ? 'border-cyan-400 bg-slate-700/50' : 'border-slate-600 hover:border-cyan-500 hover:bg-slate-700/30'}`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onClick={() => document.getElementById('file-upload')?.click()}
            >
                <input id="file-upload" type="file" className="hidden" accept="image/png, image/jpeg, image/webp" onChange={handleFileChange} />
                <div className="flex flex-col items-center justify-center text-slate-400">
                    <Icon name="uploadCloud" className="h-16 w-16 mb-4 text-slate-500" />
                    <p className="text-lg font-semibold text-slate-300">
                        <span className="text-cyan-400">Klik untuk mengunggah</span> atau seret dan lepas
                    </p>
                    <p className="text-sm">PNG, JPG, atau WEBP (maks 10MB)</p>
                </div>
            </div>
        </div>
        <div className="flex-1 w-full p-6 bg-slate-800 rounded-lg ring-1 ring-slate-700">
            <h3 className="text-lg font-semibold text-slate-200 mb-3">Panduan Mengunggah</h3>
            <ul className="space-y-2 text-slate-400 text-sm">
                <li className="flex items-start gap-2">
                    <Icon name="checkCircle" className="h-4 w-4 text-green-500 mt-1 flex-shrink-0" />
                    <span>Pastikan grafik jelas dan tidak buram.</span>
                </li>
                <li className="flex items-start gap-2">
                    <Icon name="checkCircle" className="h-4 w-4 text-green-500 mt-1 flex-shrink-0" />
                    <span>Sumbu harga dan waktu harus terlihat dengan jelas.</span>
                </li>
                <li className="flex items-start gap-2">
                    <Icon name="checkCircle" className="h-4 w-4 text-green-500 mt-1 flex-shrink-0" />
                    <span>Sertakan indikator yang relevan pada grafik jika memungkinkan (misalnya, EMA, Volume).</span>
                </li>
                 <li className="flex items-start gap-2">
                    <Icon name="checkCircle" className="h-4 w-4 text-green-500 mt-1 flex-shrink-0" />
                    <span>Gunakan grafik kandil (candlestick) atau batang standar untuk hasil terbaik.</span>
                </li>
            </ul>
        </div>
    </div>
  );
};

export default UploadStep;