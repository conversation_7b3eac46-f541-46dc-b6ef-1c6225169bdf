{"compilerOptions": {"target": "ES2020", "experimentalDecorators": true, "useDefineForClassFields": false, "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "bundler", "isolatedModules": true, "moduleDetection": "force", "allowJs": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "paths": {"@/*": ["./*"]}, "allowImportingTsExtensions": true, "noEmit": true}}