
import React from 'react';
import { Icon } from './Icon';

interface ErrorDisplayProps {
  message: string;
  onReset: () => void;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ message, onReset }) => {
  return (
    <div className="flex flex-col items-center text-center py-12">
      <Icon name="xCircle" className="h-16 w-16 text-red-500 mb-4" />
      <h2 className="text-2xl font-bold text-red-400 mb-2">Analisis Gagal</h2>
      <p className="text-slate-400 max-w-md mb-8">{message}</p>
      
      <button
        onClick={onReset}
        className="flex items-center justify-center gap-2 px-6 py-3 font-semibold text-white bg-cyan-600 hover:bg-cyan-500 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-cyan-500"
      >
        <Icon name="rotateCcw" className="h-5 w-5" />
        <span>Coba Lagi</span>
      </button>
    </div>
  );
};

export default ErrorDisplay;