import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
  name: string;
}

export const Icon: React.FC<IconProps> = ({ name, className, ...props }) => {
  const icons: { [key: string]: React.ReactNode } = {
    uploadCloud: (
      <path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242" />
    ),
    checkCircle: <><circle cx="12" cy="12" r="10" /><path d="m9 12 2 2 4-4" /></>,
    xCircle: <><circle cx="12" cy="12" r="10" /><path d="m15 9-6 6" /><path d="m9 9 6 6" /></>,
    arrowRight: <path d="M5 12h14" /><path d="m12 5 7 7-7 7" />,
    rotateCcw: <><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" /><path d="M3 3v5h5" /></>,
    brainCircuit: <><path d="M12 5a3 3 0 1 0-5.993.142" /><path d="M18 5a3 3 0 1 0-5.993.142" /><path d="M12 19a3 3 0 1 0-5.993-.142" /><path d="M18 19a3 3 0 1 0-5.993-.142" /><path d="M12 12a3 3 0 1 0-5.993-.142" /><path d="M18 12a3 3 0 1 0-5.993-.142" /><path d="M6 7.86V5" /><path d="M6 19v-2.86" /><path d="M18 7.86V5" /><path d="M18 19v-2.86" /><path d="M12 7.86V5" /><path d="M12 19v-2.86" /><path d="M6.007 12h-1.5" /><path d="M19.5 12h-1.5" /></>,
    trendingUp: <polyline points="22 7 13.5 15.5 8.5 10.5 2 17" /><polyline points="16 7 22 7 22 13" />,
    trendingDown: <polyline points="22 17 13.5 8.5 8.5 13.5 2 7" /><polyline points="16 17 22 17 22 11" />,
    moveHorizontal: <><polyline points="18 8 22 12 18 16" /><polyline points="6 8 2 12 6 16" /><line x1="2" x2="22" y1="12" y2="12" /></>,
    barChart: <path d="M3 3v18h18" /><path d="M9 18v-6" /><path d="M15 18v-10" /><path d="M21 18v-4" />,
    box: <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z" />,
    dollarSign: <><line x1="12" x2="12" y1="2" y2="22" /><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" /></>,
    target: <><circle cx="12" cy="12" r="10"/><circle cx="12" cy="12" r="6"/><circle cx="12" cy="12" r="2"/></>,
    shield: <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>,
  };

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      {icons[name] || <circle cx="12" cy="12" r="10" />}
    </svg>
  );
};